<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.4</version>
        <relativePath/>
    </parent>

    <groupId>com.sarp</groupId>
    <artifactId>sarp-parent</artifactId>
    <name>sarp-parent</name>
    <version>1.1.1</version>
    <packaging>pom</packaging>

    <distributionManagement>
        <repository>
            <id>github-sarp-parent</id>
            <name>GitHub SARP Apache Maven Packages</name>
            <url>https://maven.pkg.github.com/sarp-dev-team/sarp-parent</url>
        </repository>
    </distributionManagement>

    <properties>
        <maven.compiler.release>21</maven.compiler.release>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <jackson-databind-nullable-version>0.2.6</jackson-databind-nullable-version>
        <swagger-annotations-version>2.2.20</swagger-annotations-version>
        <openapi-generator.version>7.12.0</openapi-generator.version>
        <acr.registry>acrsarpdev.azurecr.io</acr.registry>
        <jib.plugin.version>3.4.0</jib.plugin.version>
        <jib.image>${acr.registry}/${project.artifactId}</jib.image>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- OpenAPI/Swagger dependencies -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.8.8</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable-version}</version>
        </dependency>

        <!-- Jakarta dependencies -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>${jakarta-validation.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta-servlet.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Other common dependencies -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- Define plugin configurations but don't apply them -->
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>

                <!-- Spring Boot Maven Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <release>${maven.compiler.release}</release>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${lombok-mapstruct-binding.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>

                <!-- OpenAPI Generator Plugin -->
                <plugin>
                    <groupId>org.openapitools</groupId>
                    <artifactId>openapi-generator-maven-plugin</artifactId>
                    <version>${openapi-generator.version}</version>
                    <configuration>
                        <inputSpec>${project.basedir}/src/main/resources/openapi/api/specs.yml</inputSpec>
                        <generatorName>spring</generatorName>
                        <library>spring-boot</library>
                        <output>${project.build.directory}/generated-sources/openapi</output>
                        <apiPackage>com.sarp.generated.openapi.api.paths</apiPackage>
                        <modelPackage>com.sarp.generated.openapi.api.dto</modelPackage>
                        <typeMappings>
                            <typeMapping>BaseResponse=BaseResponseDTO</typeMapping>
                            <typeMapping>Error=ErrorDTO</typeMapping>
                            <typeMapping>Metadata=MetadataDTO</typeMapping>
                        </typeMappings>
                        <importMappings>
                            <importMapping>BaseResponseDTO=com.sarp.core.dto.BaseResponseDTO</importMapping>
                            <importMapping>ErrorDTO=com.sarp.core.dto.ErrorDTO</importMapping>
                            <importMapping>MetadataDTO=com.sarp.core.dto.MetadataDTO</importMapping>
                        </importMappings>
                        <configOptions>
                            <useBuilder>true</useBuilder>
                            <delegatePattern>false</delegatePattern>
                            <interfaceOnly>true</interfaceOnly>
                            <useSpringBoot3>true</useSpringBoot3>
                            <documentationProvider>springdoc</documentationProvider>
                            <openApiNullable>false</openApiNullable>
                            <skipDefaultInterface>true</skipDefaultInterface>
                            <useTags>true</useTags>
                            <useJakartaEe>true</useJakartaEe>
                            <dateLibrary>java8</dateLibrary>
                            <useOneOfDiscriminatorLookup>true</useOneOfDiscriminatorLookup>
                            <legacyDiscriminatorBehavior>false</legacyDiscriminatorBehavior>
                            <dateTimeType>java.time.LocalDateTime</dateTimeType>
                            <sourceFolder>/</sourceFolder>
                        </configOptions>
                    </configuration>
                </plugin>

                <!-- Build Helper Maven Plugin -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin.version}</version>
                    <configuration>
                        <sources>
                            <source>${project.build.directory}/generated-sources/openapi</source>
                        </sources>
                    </configuration>
                </plugin>

                <!-- Google Jib Plugin -->
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>${jib.plugin.version}</version>
                    <configuration>
                        <from>
                            <image>eclipse-temurin:21-jdk</image>
                        </from>
                        <to>
                            <image>${jib.image}</image>
                            <auth>
                                <username>${env.ACR_USERNAME}</username>
                                <password>${env.ACR_PASSWORD}</password>
                            </auth>
                        </to>
                        <container>
                            <ports>
                                <port>8080</port>
                            </ports>
                            <!-- Optional: override mainClass if needed -->
                        </container>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- Spotless: check code formatting -->
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.44.0</version>
                <executions>
                    <!-- Spotless Check (fails build if not formatted) -->
                    <execution>
                        <id>spotless-check</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <java>
                        <palantirJavaFormat>
                            <version>2.68.0</version>
                            <style>PALANTIR</style>
                        </palantirJavaFormat>
                    </java>
                </configuration>
            </plugin>
            <!-- Checkstyle: check code style rules -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.6.0</version>
                <!-- Use latest -->
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                            <!-- Fail if style rules are violated -->
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <configLocation>google_checks.xml</configLocation>
                    <!-- or your custom checkstyle.xml -->
                    <consoleOutput>true</consoleOutput>
                    <failOnViolation>true</failOnViolation>
                    <!-- FAIL build if violation -->
                    <linkXRef>false</linkXRef>
                    <!-- Skip xref links -->
                </configuration>
            </plugin>
            <!-- JaCoCo: code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.13</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>local</spring.profiles.active>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <scope>runtime</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
