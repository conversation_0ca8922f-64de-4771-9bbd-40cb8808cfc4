# SARP Parent

This repository contains internal Maven package `sarp-parent`, published to **GitHub Packages** under the `sarp-dev-team` organization.

To use these packages in other Maven projects or to test them locally, you must configure your local Maven `settings.xml` to authenticate with GitHub Packages. Without this configuration, <PERSON><PERSON> will fail to resolve these internal dependencies.

---

## 🔐 Accessing GitHub Packages Locally

Follow these steps to make the packages available for use on your local machine:

### 1. Log in to GitHub

Use your GitHub account that ends with `_sarp` (e.g., `A-YERLIKAYA_sarp`).

### 2. Generate a Personal Access Token

1. Click your profile picture (top-right) → **Settings**
2. In the left sidebar, scroll down and select **Developer settings**
3. Go to **Personal access tokens** → **Tokens (classic)**
4. Click **Generate new token (classic)**
5. Enter a descriptive name under **Note**, and choose an appropriate **expiration date**
6. Select only the **`read:packages`** scope
7. Click **Generate token** and **copy the token** somewhere safe

### 3. Configure SSO Access

After generating the token, click **Configure SSO** and enable access for the `sarp-dev-team` organization.

### 4. Update or Create `settings.xml`

Locate (or create) the `~/.m2/settings.xml` file on your local machine.

Paste the following XML configuration:

```xml
<settings>
    <servers>
        <server>
            <id>github-sarp-parent</id>
            <username>GITHUB_USERNAME</username>
            <password>GITHUB_TOKEN</password>
        </server>
        <server>
            <id>github-sarp-commons</id>
            <username>GITHUB_USERNAME</username>
            <password>GITHUB_TOKEN</password>
        </server>
    </servers>
    <profiles>
        <profile>
            <id>sarp</id>
            <repositories>
                <repository>
                    <id>github-sarp-parent</id>
                    <name>Github SARP Parent</name>
                    <url>https://maven.pkg.github.com/sarp-dev-team/sarp-parent</url>
                </repository>
                <repository>
                    <id>github-sarp-commons</id>
                    <name>Github SARP Commons</name>
                    <url>https://maven.pkg.github.com/sarp-dev-team/sarp-commons</url>
                </repository>
            </repositories>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>sarp</activeProfile>
    </activeProfiles>
</settings>
```

## 🎨 Setting Up Formatter Locally

### Option 1: Using `palantir-java-format` IntelliJ Plugin (Most Recommended!)

1. Install the **palantir-java-format** plugin from the IntelliJ Marketplace.
2. Open **Settings** → **palantir-java-format Settings**
3. Check the box for **Enable palantir-java-format**
4. Format file using IntelliJ's built-in formatter with `⌘` + `⌥` + `L` or by right-clicking in the editor and selecting **Reformat Code**.

### Option 2: Using `spotless-applier` IntelliJ Plugin

1. Install the **spotless-applier** plugin from the IntelliJ Marketplace.
2. Format file using with `⌘` + `⌥` + `;`
3. Format all files in the project using `⌘` + `⌥` + `⇧` + `;`
4. Adjust shortcuts in the settings if needed.

### Option 3: Adding Custom External Tool in IntelliJ

1. Create an external tool in the **Settings** → **Tools** → **External Tools** section.
2. Configure the tool with the following settings:
    - **Name**: `spotless-applier`
    - **Program**: `/bin/zsh`
    - **Arguments**: `mvn spotless:apply`
    - **Working Directory**: `$ProjectFileDir$`
3. Assign a shortcut for quick access in **Settings** → **Keymap** → **External Tools**.
4. Use the shortcut to apply formatting across the project.
