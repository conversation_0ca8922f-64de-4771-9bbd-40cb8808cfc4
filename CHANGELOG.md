# Changelog: sarp-parent

All notable changes to this project will be documented in this file.

The format is based
on [infra-docs/CHANGELOG-TEMPLATE.md](https://github.com/sarp-dev-team/infra-docs/blob/main/CHANGELOG-TEMPLATE.md),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.2] - 2025-07-10

### ❌ Removed

- Removed `to.tags=latest` from jib configuration to prevent overwriting the latest tag even if `latest` is not specified while pushing.

## [1.1.1] - 2025-07-02

### 🚀 Added

- Added new dependencies:
    - `spring-boot-autoconfigure`
    - `spring-boot-configuration-processor`
- Replaced `googleJavaFormat` with `palantirJavaFormat` in spotless configuration.
- Added `jacoco-maven-plugin:0.8.13` to report code coverage for SonarQube.

### ✏️ Changed

- Bumped version of `sarp-parent` from `1.0.0` to `1.1.1`.
- Updated `springdoc-openapi-starter-webmvc-ui` version from `2.1.0` to `2.8.8`.

## [1.0.0-R1] - 2025-05-16

### 🚀 Added

- Initialized `sarp-parent` parent project.

[unreleased]: https://github.com/sarp-dev-team/sarp-parent/compare/sarp-parent-1.1.1...HEAD

[1.1.1]: https://github.com/sarp-dev-team/sarp-parent/compare/sarp-parent-1.0.0-R1..sarp-parent-1.1.1

[1.0.0-R1]: https://github.com/sarp-dev-team/sarp-parent/commits/sarp-parent-1.0.0-R1
