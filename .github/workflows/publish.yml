name: Publish Maven Package

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      version-suffix:
        description: 'Version Suffix to append to the current version. (e.g., Original Version: 1.0.0, Version Suffix: TEST-1 -> Published Version: 1.0.0-TEST-1)'
        required: true

jobs:
  publish:
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
    steps:
      - name: Verify Code Quality
        uses: sarp-dev-team/sarp-actions/.github/actions/verify@main
        with:
          maven-username: ${{ secrets.MAVEN_USERNAME }}
          maven-token: ${{ secrets.MAVEN_PASSWORD }}
          sonar-host-url: ${{ vars.SONAR_HOST_URL }}
          sonar-token: ${{ secrets.SONAR_TOKEN }}
          sonar-project-key: ${{ secrets.SONAR_PROJECT_KEY }}
          sonar-project-name: ${{ github.event.repository.name }}
          branch-name: ${{ github.ref_name }}

      - name: Publish Maven Package
        uses: sarp-dev-team/sarp-actions/.github/actions/publish-maven-packages@main
        with:
          username: ${{ secrets.MAVEN_USERNAME }}
          token: ${{ secrets.MAVEN_PASSWORD }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  manual-publish:
    if: github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Manual Publish Maven Package
        uses: sarp-dev-team/sarp-actions/.github/actions/manual-publish-maven-packages@main
        with:
          username: ${{ secrets.MAVEN_USERNAME }}
          token: ${{ secrets.MAVEN_PASSWORD }}
          version-suffix: ${{ inputs.version-suffix }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
