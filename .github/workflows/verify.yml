name: Verify Code Quality

on:
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  verify:
    runs-on: ubuntu-latest

    steps:
      - name: Verify Code Quality
        uses: sarp-dev-team/sarp-actions/.github/actions/verify@main
        with:
          maven-username: ${{ secrets.MAVEN_USERNAME }}
          maven-token: ${{ secrets.MAVEN_PASSWORD }}
          sonar-host-url: ${{ vars.SONAR_HOST_URL }}
          sonar-token: ${{ secrets.SONAR_TOKEN }}
          sonar-project-key: ${{ secrets.SONAR_PROJECT_KEY }}
          sonar-project-name: ${{ github.event.repository.name }}
          pr-number: ${{ github.event.pull_request.number || '' }}
          pr-branch: ${{ github.event.pull_request.head.ref || '' }}
          pr-base: ${{ github.event.pull_request.base.ref || '' }}
